<template>
	<view>
		<view class="visit-detail-container">
			<!-- 顶部导航 -->
			<cu-custom :bgColor="NavBarColor" isBack>
				<block slot="backText">返回</block>
				<block slot="content">拜访详情</block>
			</cu-custom>
			<!-- 主内容区 -->
			<view class="main-content">
				<!-- 客户信息卡片 -->
				<view class="card">
					<view class="card-header">
						<view class="flex align-center">
							<text class="cuIcon-group text-blue margin-right-xs"></text>
							<text>客户信息</text>
						</view>
						<text class="cuIcon-add text-blue" @tap="showFinishModal"
							v-if="visitDetail.visitStatus != 4"></text>
					</view>
					<view class="card-body">
						<view class="customer-info">
							<view class="customer-avatar">
								{{ visitDetail.customerName ? visitDetail.customerName.charAt(0) : 'C' }}
							</view>
							<view class="customer-details">
								<view class="customer-name">{{ visitDetail.customerName }}</view>
								<view class="customer-meta">
									<text class="cuIcon-phone text-gray margin-right-xs"></text>
									<text>{{ visitDetail.customerPhone || '暂无电话' }}</text>
								</view>
								<view class="customer-meta">
									<text class="cuIcon-location text-gray margin-right-xs"></text>
									<text>{{ visitDetail.customerAddress || '暂无地址' }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 拜访计划信息 -->
				<!-- <view class="card">
					<view class="card-header">
						<view class="flex align-center">
							<text class="cuIcon-calendar text-orange margin-right-xs"></text>
							<text>拜访计划</text>
						</view>
						<view class="visit-status" :class="getStatusClass(visitDetail.visitStatus)">
							{{ getStatusText(visitDetail.visitStatus) }}
						</view>
					</view>
					<view class="card-body">
						<view class="plan-info">
							<view class="info-item">
								<text class="info-label">拜访时间</text>
								<text class="info-value">{{ visitDetail.visitDate }} {{ visitDetail.visitTime }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">拜访地点</text>
								<text class="info-value">{{ visitDetail.visitAddress }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">拜访目的</text>
								<text class="info-value">{{ visitDetail.visitPurpose }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">预计时长</text>
								<text class="info-value">{{ visitDetail.estimatedDuration }}分钟</text>
							</view>
							<view class="info-item">
								<text class="info-label">创建时间</text>
								<text class="info-value">{{ visitDetail.createTime }}</text>
							</view>
						</view>
					</view>
				</view> -->

				<!-- 拜访记录 -->
				<view class="card" v-if="visitDetail.visitRecord && visitDetail.visitRecord.length > 0">
					<view class="card-header">
						<view class="flex align-center">
							<text class="cuIcon-edit text-green margin-right-xs"></text>
							<text>拜访记录</text>
						</view>
					</view>
					<view class="card-body">
						<view v-for="(record, recordIndex) in visitDetail.visitRecord" :key="recordIndex"
							class="record-item">
							<view class="record-info">
								<view class="info-item" v-if="record.visitContent">
									<text class="info-label">拜访内容</text>
									<text class="info-value">{{ record.visitContent }}</text>
								</view>
								<view class="info-item" v-if="record.visitAddress">
									<text class="info-label">拜访地点</text>
									<text class="info-value">{{ record.visitAddress }}</text>
								</view>
								<view class="info-item" v-if="record.visitPhotos">
									<text class="info-label">拜访照片</text>
									<view class="photo-list">
										<view class="photo-item"
											v-for="(photo, photoIndex) in getRecordPhotos(record.visitPhotos)"
											:key="photoIndex">
											<image :src="getFullImageUrl(photo)" class="photo-image" mode="aspectFill"
												@tap="previewRecordImage(record.visitPhotos, photoIndex)"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<button v-if="visitDetail.visitStatus == 1" class="cu-btn block line-red" @tap="cancelVisit">
						取消拜访
					</button>
				</view>
			</view>
		</view>

		<!-- 完成拜访弹窗 -->
		<view v-if="showFinishPlanModal" class="modal-overlay" @tap="hideFinishModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">完成拜访</text>
					<text class="cuIcon-close text-gray" @tap="hideFinishModal"></text>
				</view>
				<view class="modal-body">
					<view class="form-group">
						<text class="form-label">拜访内容与成果</text>
						<textarea v-model="finishForm.visitContent" placeholder="请输入拜访内容"
							class="form-textarea"></textarea>
					</view>

					<view class="form-group">
						<text class="form-label">拜访照片</text>
						<view class="photo-upload-area">
							<view class="photo-list">
								<view class="photo-item" v-for="(image, index) in uploadedImages" :key="index">
									<image :src="image.tempPath" class="photo-image" mode="aspectFill"
										@tap="previewUploadedImage(image.tempPath)"></image>
									<view class="photo-delete" @tap="deleteUploadedImage(index)">
										<text class="cuIcon-close"></text>
									</view>
								</view>
								<view class="photo-upload-btn" v-if="uploadedImages.length < 3" @tap="chooseImage">
									<text class="cuIcon-camera"></text>
								</view>
							</view>
						</view>
					</view>

					<view class="form-group">
						<text class="form-label">当前位置</text>
						<view class="location-display">
							<text class="cuIcon-locationfill text-blue"></text>
							<text class="location-text">{{ finishForm.finishAddress || '正在获取位置...' }}</text>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="cu-btn line-gray" @tap="hideFinishModal">取消</button>
					<button class="cu-btn bg-blue" @tap="submitFinish" :disabled="finishSubmitting">
						{{ finishSubmitting ? '提交中...' : '提交' }}
					</button>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
import configService from '@/common/service/config.service.js';
import { imageWatermark } from '@/common/util/util.js';

export default {
	data() {
		return {
			visitId: '',
			visitDetail: {},

			// 状态映射
			statusMap: {
				1: { text: '待拜访', class: 'status-planned' },
				2: { text: '进行中', class: 'status-progress' },
				3: { text: '已完成', class: 'status-completed' },
				4: { text: '已取消', class: 'status-canceled' }
			},

			// 修改计划相关
			showEditPlanModal: false,
			editSubmitting: false,
			editForm: {
				id: '',
				customerCode: '',
				customerName: '',
				visitDate: '',
				visitTime: '',
				visitAddress: '',
				visitPurpose: '',
				purposeCode: '',
				estimatedDuration: 60,
				longitude: '',
				latitude: '',
				addressName: ''
			},

			// 客户列表和搜索（修改用）
			editCustomerList: [],
			editCustomerSearchResults: [],
			editCustomerSearchKeyword: '',
			editSelectedCustomer: null,
			editShowCustomerList: false,
			editInputFocused: false,
			editSearchTimer: null,

			// 拜访目的列表（修改用）
			editVisitPurposeList: [],
			editSelectedPurpose: null,
			editPurposeIndex: 0,

			// 地点选择相关（修改用）
			editShowLocationPicker: false,
			editLocationSearchKeyword: '',
			editLocationSearchResults: [],
			editLocationInputFocused: false,
			editSelectedLocation: null,
			editLocationSearchTimer: null,
			editLocationMapCenter: {
				longitude: 116.397428,
				latitude: 39.90923
			},
			editLocationMapScale: 16,
			editLocationMarkers: [],

			// 高德地图配置
			amapKey: 'bf98482bb3a3ee9c97a371ffe8fbc9c2',

			// 完成拜访相关
			showFinishPlanModal: false,
			finishSubmitting: false,
			finishForm: {
				visitContent: '',
				visitPhotos: '',
				longitude: '',
				latitude: '',
				finishAddress: ''
			},
			uploadedImages: [], // 用于存储上传的图片
			res: null,
		}
	},

	onLoad(options) {
		this.visitId = options.id;
		this.loadVisitDetail();
		this.getLocationInfo();
	},

	methods: {
		formatDate() {
			const date = new Date();
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
			const weekday = weekdays[date.getDay()];
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}年${month}月${day}日 ${weekday} ${hours}:${minutes}:${seconds}`;
		},

		// 获取图片完整URL
		getFullImageUrl(url) {
			if (!url) return '';
			if (url.startsWith('http')) {
				return url;
			}
			const domain = configService.staticDomainURL || '';
			// 移除 domain 尾部的 /
			const cleanDomain = domain.endsWith('/') ? domain.slice(0, -1) : domain;
			// 移除 url 头部的 /
			const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

			if (!cleanDomain) return `/${cleanUrl}`;

			return `${cleanDomain}/${cleanUrl}`;
		},

		// 预览记录中的图片
		previewRecordImage(photos, index) {
			const photoArray = this.getRecordPhotos(photos);
			const urls = photoArray.map(url => this.getFullImageUrl(url));
			uni.previewImage({
				current: urls[index],
				urls: urls
			});
		},

		getRecordPhotos(photos) {
			if (photos && typeof photos === 'string') {
				return photos.split(',').filter(url => url.trim() !== '');
			}
			return [];
		},

		// 获取位置信息
		getLocationInfo() {
			const that = this
			if (this.$wx) {
				this.$wx.getLocation({
					type: 'gcj02',
					success: (res) => {
						that.res = res
						that.finishForm.latitude = res.latitude;
						that.finishForm.longitude = res.longitude;
						that.reverseGeocode();
					},
					fail: (error) => {
						console.error('获取位置失败：', error);
						this.statusMessage = '获取位置信息失败，请检查定位权限';
						this.statusType = 'error';
					}
				});
			}
		},
		// 逆地址解析
		reverseGeocode() {
			this.$http.get("/pm/wx/getAddress", {
				params: {
					longitude: this.finishForm.longitude,
					latitude: this.finishForm.latitude
				}
			}).then(res => {
				if (res.data && res.data.result && res.data.result.regeocode) {
					this.finishForm.finishAddress = res.data.result.regeocode.formatted_address;
					console.log("🚀 ~ reverseGeocode ~ this.finishForm:", this.finishForm)
				} else {
					this.finishForm.finishAddress = '位置解析失败';
				}
			}).catch(error => {
				console.error('逆地址解析失败：', error);
				this.finishForm.finishAddress = '位置获取失败';
			});
		},

		// 选择图片
		chooseImage() {
			const that = this
			uni.chooseImage({
				count: 3 - this.uploadedImages.length,
				sizeType: ['compressed'],
				sourceType: ['camera', 'album'],
				success: async (res) => {
					// res.tempFilePaths.forEach((tempPath) => {
					// 	this.uploadImage(tempPath);
					// });
					const mFile = res.tempFiles[0]
					const imgBlob = await imageWatermark(mFile, that.formatDate(), that.finishForm.finishAddress,)
					const wmFile = new File([imgBlob], mFile.name, {
						type: mFile.type,
						lastModified: Date.now(),
					})
					console.log("🚀 ~ chooseImage ~ wmFile:", wmFile)
					uni.showLoading({
						title: '上传中',
						mask: true,
					});
					uni.uploadFile({
						url: configService.apiUrl + '/sys/obs/upload',
						file: wmFile,
						name: 'file',
						header: {
							// 可以添加认证头等
							'X-Access-Token': uni.getStorageSync('Access-Token') || ''
						},
						success: (uploadRes) => {
							try {
								const result = JSON.parse(uploadRes.data);
								if (result.success) {
									// 根据实际返回的数据结构调整
									const imageUrl = result.result.objectKey;
									const previewUrl = result.result.previewUrl;
									that.uploadedImages.push({
										url: imageUrl,
										tempPath: previewUrl
									});
									uni.showToast({
										title: '上传成功',
										icon: 'success'
									});
								} else {
									throw new Error(result.message || '上传失败');
								}
							} catch (error) {
								console.error('解析上传结果失败：', error);
								uni.showToast({
									title: '上传失败',
									icon: 'none'
								});
							}
						},
						fail: (error) => {
							console.error('上传失败：', error);
							uni.showToast({
								title: '上传失败，请重试',
								icon: 'none'
							});
						},
						complete: () => {
							uni.hideLoading();
						}
					});
				}
			});
		},

		// 上传图片
		uploadImage(tempFilePath) {
			uni.showLoading({
				title: '上传中...'
			});
			uni.uploadFile({
				url: this.$config.apiUrl + '/sys/obs/upload',
				filePath: tempFilePath,
				name: 'file',
				header: {
					'X-Access-Token': uni.getStorageSync('Access-Token') || ''
				},
				success: (uploadRes) => {
					try {
						const result = JSON.parse(uploadRes.data);
						if (result.success) {
							const imageUrl = result.result.objectKey;
							this.uploadedImages.push({
								url: imageUrl,
								tempPath: tempFilePath
							});
							this.$tip.success('上传成功');
						} else {
							throw new Error(result.message || '上传失败');
						}
					} catch (e) {
						this.$tip.error('上传失败');
					}
				},
				fail: (err) => {
					this.$tip.error('上传失败，请重试');
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},

		// 删除已上传的图片
		deleteUploadedImage(index) {
			this.uploadedImages.splice(index, 1);
		},

		// 预览已上传的图片
		previewUploadedImage(currentUrl) {
			const urls = this.uploadedImages.map(img => img.tempPath);
			uni.previewImage({
				current: currentUrl,
				urls: urls
			});
		},

		// 获取客户类型文本
		getCustomerTypeText(type) {
			const typeMap = {
				1: 'KA卖场',
				2: 'BC网点'
			};
			return typeMap[type] || '未知';
		},

		// 调用高德地图POI搜索API（修改用）
		async searchEditPOI(keyword) {
			const url = `https://restapi.amap.com/v3/place/text`;
			const params = {
				key: this.amapKey,
				keywords: keyword,
				city: '全国',
				types: '',
				page: 1,
				size: 20,
				extensions: 'base'
			};

			const queryString = Object.keys(params)
				.map(key => `${key}=${encodeURIComponent(params[key])}`)
				.join('&');

			const fullUrl = `${url}?${queryString}`;

			return new Promise((resolve, reject) => {
				uni.request({
					url: fullUrl,
					method: 'GET',
					success: (res) => {
						if (res.statusCode === 200 && res.data.status === '1') {
							resolve(res.data);
						} else {
							reject(new Error(res.data.info || '搜索失败'));
						}
					},
					fail: (error) => {
						reject(error);
					}
				});
			});
		},

		// 选择地点（修改用）
		selectEditLocation(location) {
			this.editSelectedLocation = location;
			this.editLocationSearchKeyword = location.name;
			this.editLocationSearchResults = [];

			// 更新地图中心点和标记
			this.editLocationMapCenter = {
				longitude: location.longitude,
				latitude: location.latitude
			};

			this.editLocationMarkers = [{
				id: 1,
				longitude: location.longitude,
				latitude: location.latitude,
				iconPath: '/static/images/location-marker.png',
				width: 30,
				height: 30,
				title: location.name
			}];

			console.log('选择地点:', location);
		},

		// 地图点击事件（修改用）
		onEditMapTap(e) {
			console.log('地图点击:', e);
			const { longitude, latitude } = e.detail;
			this.reverseEditGeocode(longitude, latitude);
		},

		// 地图标记点击（修改用）
		onEditLocationMarkerTap(e) {
			console.log('标记点击:', e);
		},

		// 逆地理编码（修改用）
		async reverseEditGeocode(longitude, latitude) {
			try {
				const url = `https://restapi.amap.com/v3/geocode/regeo`;
				const params = {
					key: this.amapKey,
					location: `${longitude},${latitude}`,
					poitype: '',
					radius: 1000,
					extensions: 'base',
					batch: false,
					roadlevel: 0
				};

				const queryString = Object.keys(params)
					.map(key => `${key}=${encodeURIComponent(params[key])}`)
					.join('&');

				const fullUrl = `${url}?${queryString}`;

				uni.request({
					url: fullUrl,
					method: 'GET',
					success: (res) => {
						if (res.statusCode === 200 && res.data.status === '1') {
							const regeocode = res.data.regeocode;
							const location = {
								id: Date.now().toString(),
								name: regeocode.formatted_address,
								address: regeocode.formatted_address,
								longitude: longitude,
								latitude: latitude,
								distance: ''
							};

							this.selectEditLocation(location);
						}
					},
					fail: (error) => {
						console.error('逆地理编码失败:', error);
					}
				});
			} catch (error) {
				console.error('逆地理编码异常:', error);
			}
		},

		// ==================== 完成拜访相关方法 ====================

		// 显示完成拜访弹窗
		showFinishModal() {
			this.resetFinishForm();
			this.showFinishPlanModal = true;
		},

		// 隐藏完成拜访弹窗
		hideFinishModal() {
			this.showFinishPlanModal = false;
			this.resetFinishForm();
		},

		// 重置完成表单
		resetFinishForm() {
			this.finishForm = {
				visitContent: '',
				visitPhotos: '',
				longitude: '',
				latitude: '',
				finishAddress: ''
			};
			this.getLocationInfo()
			this.uploadedImages = [];
		},

		// 设置满意度
		setSatisfaction(star) {
			this.finishForm.satisfaction = star;
		},

		// 提交完成拜访
		async submitFinish() {
			this.finishSubmitting = true;
			// 验证
			if (!this.finishForm.visitContent.trim()) {
				this.$tip.error('请输入拜访内容');
				this.finishSubmitting = false;
				return;
			}
			if (this.uploadedImages.length === 0) {
				this.$tip.error('请上传至少一张拜访照片');
				this.finishSubmitting = false;
				return;
			}
			if (!this.finishForm.longitude || !this.finishForm.latitude) {
			 	this.$tip.error('无法获取当前位置，请稍后重试');
				this.finishSubmitting = false;
			    return;
			}

			try {
				const params = {
					visitContent: this.finishForm.visitContent,
					visitPhotos: this.uploadedImages.map(img => img.url).join(','),
					longitude: this.finishForm.longitude,
					latitude: this.finishForm.latitude,
					finishAddress: this.finishForm.finishAddress,
				};

				const response = await this.$http.post(`/pm/visit/plan/finish/${this.visitId}`, params);
				if (response.data.success) {
					this.$tip.success('拜访已完成');
					this.hideFinishModal();
					await this.loadVisitDetail();
				} else {
					this.$tip.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('完成拜访失败:', error);
				this.$tip.error('操作失败');
			} finally {
				this.finishSubmitting = false;
			}
		},
		// 加载拜访详情
		async loadVisitDetail() {
			try {
				const response = await this.$http.get(`/pm/visit/plan/detail/${this.visitId}`);
				if (response.data.success) {
					this.visitDetail = response.data.result;
					console.log('拜访详情加载成功:', this.visitDetail);
				} else {
					this.$tip.error('加载详情失败');
				}
			} catch (error) {
				console.error('加载拜访详情失败:', error);
				this.$tip.error('加载详情失败');
			}
		},

		// 获取状态文本
		getStatusText(status) {
			return this.statusMap[status]?.text || '未知';
		},

		// 获取状态样式类
		getStatusClass(status) {
			return this.statusMap[status]?.class || '';
		},

		// 开始拜访
		async startVisit() {
			try {
				const response = await this.$http.post(`/pm/visit/plan/start/${this.visitId}`);
				if (response.data.success) {
					this.$tip.success('拜访已开始');
					await this.loadVisitDetail();
				} else {
					this.$tip.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('开始拜访失败:', error);
				this.$tip.error('操作失败');
			}
		},

		// 取消拜访
		async cancelVisit() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消这次拜访吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await this.$http.post(`/pm/visit/plan/cancel/${this.visitId}`);
							if (response.data.success) {
								this.$tip.success('拜访已取消');
								await this.loadVisitDetail();
							} else {
								this.$tip.error(response.data.message || '操作失败');
							}
						} catch (error) {
							console.error('取消拜访失败:', error);
							this.$tip.error('操作失败');
						}
					}
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.visit-detail-container {
	background-color: #f5f7fa;
	min-height: 100vh;
}

.main-content {
	padding: 20rpx;
}

.record-item:not(:last-child) {
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 30rpx;
	margin-bottom: 30rpx;
}

/* 卡片样式 */
.card {
	background: white;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	margin-bottom: 30rpx;
	overflow: hidden;
}

.card-header {
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 600;
	font-size: 32rpx;
}

.card-body {
	padding: 30rpx;
}

/* 客户信息 */
.customer-info {
	display: flex;
	align-items: center;
}

.customer-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 24rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-weight: bold;
	font-size: 40rpx;
	margin-right: 30rpx;
}

.customer-details {
	flex: 1;
}

.customer-name {
	font-size: 36rpx;
	font-weight: 600;
	margin-bottom: 12rpx;
}

.customer-meta {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

/* 信息项 */
.info-item {
	display: flex;
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.info-label {
	width: 160rpx;
	font-size: 28rpx;
	color: #666;
	flex-shrink: 0;
}

.info-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	word-break: break-all;
}

/* 状态样式 */
.visit-status {
	font-size: 24rpx;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-weight: 500;
}

.status-planned {
	background: #e3f2fd;
	color: #1976d2;
}

.status-progress {
	background: #e8f5e9;
	color: #388e3c;
}

.status-completed {
	background: #f3e5f5;
	color: #7b1fa2;
}

.status-canceled {
	background: #ffebee;
	color: #d32f2f;
}

/* 操作按钮 */
.action-buttons {
	padding: 0 20rpx 40rpx;
}

/* 客户搜索样式 */
.customer-search-container {
	position: relative;
}

.search-input-wrapper {
	position: relative;
}

.customer-search-input {
	width: 100%;
	padding: 24rpx 60rpx 24rpx 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	box-sizing: border-box;
}

.search-icon {
	position: absolute;
	right: 24rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
}

.customer-list-dropdown {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: white;
	border: 1px solid #e0e0e0;
	border-top: none;
	border-radius: 0 0 12rpx 12rpx;
	max-height: 400rpx;
	overflow-y: auto;
	z-index: 1000;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.customer-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;

	&:last-child {
		border-bottom: none;
	}

	&:hover {
		background: #f8f9fa;
	}
}

.customer-info {
	flex: 1;
}

.customer-name {
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 4rpx;
	display: block;
}

.customer-code {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.customer-type {
	font-size: 22rpx;
	color: #999;
	padding: 4rpx 12rpx;
	background: #f0f0f0;
	border-radius: 8rpx;
}

.no-results {
	text-align: center;
	padding: 40rpx;
	color: #999;
}

.selected-customer {
	background: #f0f8ff;
	border: 1px solid #007aff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-top: 16rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.selected-info {
	flex: 1;
}

.selected-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #007aff;
	margin-bottom: 4rpx;
	display: block;
}

.selected-code {
	font-size: 24rpx;
	color: #666;
	display: block;
}

/* 地点选择样式 */
.location-selector {
	width: 100%;
}

.location-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	font-size: 28rpx;
	cursor: pointer;
}

.location-text {
	color: #333;
	flex: 1;
}

.location-placeholder {
	color: #999;
	flex: 1;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99;
	padding: 40rpx;
	backdrop-filter: blur(4rpx);
}

.modal-content {
	background: white;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	transform: scale(1);
	animation: modalShow 0.3s ease-out;
}

.modal-header {
	padding: 40rpx;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
}

.modal-body {
	flex: 1;
	padding: 40rpx;
	overflow-y: auto;
}

.modal-footer {
	padding: 30rpx 40rpx;
	border-top: 1px solid #f0f0f0;
	display: flex;
	gap: 20rpx;
}

.modal-footer .cu-btn {
	flex: 1;
}

/* 表单样式 */
.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 16rpx;
	color: #333;
}

.form-input,
.form-textarea {
	width: 100%;
	padding: 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	box-sizing: border-box;
}

.form-textarea {
	width: 100%;
	height: 180rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	padding: 16rpx;
	font-size: 28rpx;
	background: #f8f8f8;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	font-size: 28rpx;
}

/* 满意度评分 */
.satisfaction-rating {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx 0;
}

.star-item {
	font-size: 60rpx;
	color: #ddd;
	cursor: pointer;
	transition: color 0.3s ease;

	&.active {
		color: #ffb400;
	}

	&:hover {
		color: #ffb400;
	}
}

@keyframes modalShow {
	from {
		opacity: 0;
		transform: scale(0.8);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

/* 地点选择弹窗样式 */
.location-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999999;
	padding: 20rpx;
}

.location-modal-content {
	background: white;
	border-radius: 24rpx;
	width: 100%;
	max-width: 700rpx;
	height: 80vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.location-modal-header {
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.location-modal-title {
	font-size: 32rpx;
	font-weight: 600;
}

.location-search-container {
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f0f0f0;
	position: relative;
}

.location-search-input {
	width: 100%;
	padding: 20rpx 60rpx 20rpx 20rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
}

.location-search-icon {
	position: absolute;
	right: 50rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
}

.location-map-container {
	height: 300rpx;
	margin: 0 30rpx;
	border-radius: 12rpx;
	overflow: hidden;
	background: #f5f5f5;
}

.location-map {
	width: 100%;
	height: 100%;
}

.location-results-container {
	flex: 1;
	padding: 20rpx 30rpx;
	overflow-y: auto;
}

.location-results-list {
	max-height: 300rpx;
	overflow-y: auto;
}

.location-result-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;

	&:last-child {
		border-bottom: none;
	}

	&:hover {
		background: #f8f9fa;
	}
}

.location-result-info {
	flex: 1;
}

.location-result-name {
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 4rpx;
	display: block;
}

.location-result-address {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.location-result-distance {
	font-size: 22rpx;
	color: #999;
	margin-left: 20rpx;
}

.no-location-results {
	text-align: center;
	padding: 40rpx;
	color: #999;
}

.selected-location-info {
	background: #f0f8ff;
	border: 1px solid #007aff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-top: 20rpx;
}

.selected-location-details {
	display: flex;
	flex-direction: column;
}

.selected-location-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #007aff;
	margin-bottom: 4rpx;
}

.selected-location-address {
	font-size: 24rpx;
	color: #666;
}

.location-modal-footer {
	padding: 30rpx;
	border-top: 1px solid #f0f0f0;
	display: flex;
	gap: 20rpx;
}

.location-modal-footer .cu-btn {
	flex: 1;
}

.photo-upload-area {
	width: 100%;
}

.photo-list {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.photo-item {
	position: relative;
	width: 150rpx;
	height: 150rpx;
}

.photo-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.photo-delete {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 36rpx;
	height: 36rpx;
	background: rgba(255, 0, 0, 0.7);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 20rpx;
}

.photo-upload-btn {
	width: 150rpx;
	height: 150rpx;
	border: 1px dashed #cccccc;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	color: #cccccc;
	background: #f8f8f8;
}

.location-display {
	display: flex;
	align-items: center;
	padding: 16rpx;
	background: #f8f8f8;
	border-radius: 12rpx;
}

.location-text {
	margin-left: 10rpx;
	font-size: 28rpx;
	color: #666;
}
</style>
